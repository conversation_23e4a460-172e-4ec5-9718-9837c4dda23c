# 销售合同字段对应关系文档

## 一、表头字段对应关系

### 基本信息对应关系
| 数据源字段 | 数据源字段名称 | 表头字段 | 表头字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| FBillNo | 单据编号 | sf_h_no | 客户交货排期单号 | 合同编号对应排期单号 |
| FCustId | CRM客户 | client_no | 客户编号 | 客户信息对应 |
| - | - | client_name | 客户名称 | 需要关联客户表获取名称 |
| FSALEORGID | 销售组织 | company_no | 公司编号 | 销售组织对应公司编号 |
| - | - | company_name | 公司名称 | 需要关联组织表获取名称 |
| FCreateDate | 创建日期 | sf_h_rcv_datetime | 接单日期 | 创建日期对应接单日期 |
| FDocumentStatus | 单据状态 | sf_h_status | 是否生效 | 单据状态对应生效状态 |
| - | - | sf_h_status_name | 生效 | 状态名称 |
| F_PAEZ_Remarks | 备注 | cr_sf_h_rmk01 | 备注01 | 备注信息对应 |

### 创建和更新信息对应关系
| 数据源字段 | 数据源字段名称 | 表头字段 | 表头字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| FCreatorId | 创建人 | crt_user | 创建人ID | 创建人ID对应 |
| - | - | crt_user_name | 创建人姓名 | 需要关联用户表获取姓名 |
| - | - | crt_user_no | 创建人编号 | 需要关联用户表获取编号 |
| FCreateDate | 创建日期 | crt_time | 创建时间 | 创建时间对应 |
| - | - | crt_host | 创建人主机 | 系统字段 |
| FModifierId | 最后修改人 | upd_user | 更新人ID | 修改人对应更新人 |
| - | - | upd_user_name | 更新人名称 | 需要关联用户表获取姓名 |
| - | - | upd_user_no | 更新人编号 | 需要关联用户表获取编号 |
| FModifyDate | 最后修改日期 | upd_time | 更新时间 | 修改时间对应更新时间 |
| - | - | upd_host | 更新人主机 | 系统字段 |

### 计划时间对应关系
| 数据源字段 | 数据源字段名称 | 表头字段 | 表头字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| F_PAEZ_jhrq | 交货时间 | as_plan_start_datetime | 排产计划开始时间 | 交货时间可对应计划开始时间 |
| F_PAEZ_Date | 货好日 | as_plan_end_datetime | 排产计划完成时间 | 货好日对应计划完成时间 |
| - | - | mtr_set_date | 物料齐套日期 | 需要根据业务逻辑计算 |

## 二、明细字段对应关系

### 基本明细信息对应关系
| 数据源字段 | 数据源字段名称 | 明细字段 | 明细字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| FEntryID | 实体主键 | sf_b_id | 销售预测表身明细ID | 明细ID对应 |
| FMaterialId | 物料编码 | part_no | 品号 | 物料编码对应品号 |
| FMaterialName | 物料名称 | part_name | 品名 | 物料名称对应品名 |
| FMaterialModel | 规格型号 | part_spec | 规格 | 规格型号对应规格 |
| - | - | part_idt | 产品规格码 | 需要根据物料信息生成 |
| FQty | 销售数量 | sf_qty | 需求数量 | 销售数量对应需求数量 |
| FDELIVERYDATE | 要货日期 | sf_date | 需求日期 | 要货日期对应需求日期 |

### 物料属性对应关系
| 数据源字段 | 数据源字段名称 | 明细字段 | 明细字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| FF100002 | 长度 | part_length | 长度（米） | 长度属性对应 |
| - | - | part_width | 宽度（毫米） | 需要从物料属性获取 |
| - | - | part_height | 高度（毫米） | 需要从物料属性获取 |
| - | - | segment_length | 段长（米） | 需要根据业务规则计算 |
| FBomId | BOM版本 | rmh_version | BOM版本 | BOM版本直接对应 |

### 明细状态和备注对应关系
| 数据源字段 | 数据源字段名称 | 明细字段 | 明细字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| FChangeFlag | 变更标志 | sf_b_status | 表身状态 | 变更标志可对应表身状态 |
| - | - | sf_b_status_name | 表身状态名称 | 状态名称 |
| FEntryNote | 备注 | cr_sf_b_rmk01 | 备注01 | 明细备注对应 |
| - | - | cr_sf_b_rmk02 | 备注02 | 扩展备注字段 |
| - | - | cr_sf_b_rmk03 | 备注03 | 扩展备注字段 |
| - | - | cr_sf_b_rmk04 | 备注04 | 扩展备注字段 |

### 明细创建和更新信息对应关系
| 数据源字段 | 数据源字段名称 | 明细字段 | 明细字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| - | - | crt_time | 创建时间 | 继承表头创建时间 |
| - | - | crt_user | 创建人ID | 继承表头创建人 |
| - | - | crt_user_no | 创建人编号 | 继承表头创建人编号 |
| - | - | crt_user_name | 创建人姓名 | 继承表头创建人姓名 |
| - | - | crt_host | 创建人主机 | 继承表头创建人主机 |
| - | - | upd_time | 更新时间 | 继承表头更新时间 |
| - | - | upd_user | 更新人ID | 继承表头更新人 |
| - | - | upd_user_no | 更新人编号 | 继承表头更新人编号 |
| - | - | upd_user_name | 更新人名称 | 继承表头更新人名称 |
| - | - | upd_host | 更新人主机 | 继承表头更新人主机 |

### 明细计划时间对应关系
| 数据源字段 | 数据源字段名称 | 明细字段 | 明细字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| - | - | as_plan_start_datetime | 排产计划开始时间 | 继承表头计划开始时间 |
| - | - | as_plan_end_datetime | 排产计划完成时间 | 继承表头计划完成时间 |
| - | - | mtr_set_date | 物料齐套日期 | 继承表头物料齐套日期 |

### 其他字段对应关系
| 数据源字段 | 数据源字段名称 | 明细字段 | 明细字段名称 | 备注 |
|-----------|---------------|----------|-------------|------|
| - | - | sf_h_id | 客户交货排期ID | 关联表头ID |
| - | - | mrp_region_no | Mrp区域编号 | 需要根据业务规则设置 |

## 三、字段映射说明

### 1. 直接映射字段
- 这些字段可以直接从数据源映射到目标表，无需额外处理

### 2. 需要关联查询的字段
- 客户名称、公司名称、用户姓名等需要通过关联表查询获得

### 3. 需要业务逻辑计算的字段
- 物料齐套日期、产品规格码、段长等需要根据具体业务规则计算

### 4. 系统自动生成字段
- 主机信息、状态名称等由系统自动生成

### 5. 继承字段
- 明细表中的创建人、更新人、计划时间等字段继承自表头

## 四、注意事项

1. **数据完整性**：确保所有必填字段都有对应的数据源
2. **数据类型转换**：注意日期、数值等字段的数据类型转换
3. **业务规则**：某些字段需要根据具体业务规则进行计算或转换
4. **关联关系**：维护好表头和明细的关联关系
5. **状态管理**：正确处理各种状态字段的映射关系
