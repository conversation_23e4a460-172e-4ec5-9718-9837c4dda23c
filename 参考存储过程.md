
```sql
CREATE OR REPLACE FUNCTION "public"."af_pd_part_tmp_import_para_after"()
  RETURNS "pg_catalog"."varchar" AS $BODY$
	--  导入后，写入品号           
declare jsonDatas json;
        partNo varchar;
        jsonDatas2 json;
        jsonDatas_new json;
        res returntype;
begin
    
		insert into pd_part(part_status,part_no,part_name,part_spec,part_unit,
                     part_length,part_width,part_height,part_weight_net,part_weight_gross,
										 crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
										 upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
										 part_qty_decimal,
										 part_fcreateorgid,part_fuseorgid,part_f_syka_sqorgld,part_fcodetype_cmk,part_funitld_cmk,part_fforbidstatus
										 )
				select part_status,part_no,part_name,part_spec,part_unit,
                       0,0,0,0,0,
					crt_time,'sys' as crt_user, 'sys' as crt_user_no, 'sys' as crt_user_name,'sys' as crt_host,
				    upd_time, 'sys' as upd_user,'sys' as upd_user_no, 'sys' as upd_user_name,'sys' as upd_host,
					1,
					part_fcreateorgid,part_fuseorgid,part_f_syka_sqorgld,part_fcodetype_cmk,part_funitld_cmk,part_fforbidstatus
					from pd_part_temp 
					where 1=1 
				-- part_no='***********.0001' 
					on conflict (part_no) do update
                    set part_status=excluded.part_status,
					part_name=excluded.part_name,
					part_spec=excluded.part_spec,
					part_unit=excluded.part_unit,
					crt_time=excluded.crt_time,
					upd_time=excluded.upd_time,
					part_fcreateorgid=excluded.part_fcreateorgid,
					part_fuseorgid=excluded.part_fuseorgid,
					part_f_syka_sqorgld=excluded.part_f_syka_sqorgld,
					part_fcodetype_cmk=excluded.part_fcodetype_cmk,
					part_funitld_cmk=excluded.part_funitld_cmk,
					part_fforbidstatus=excluded.part_fforbidstatus;
                    truncate pd_part_temp;
  
   res:=row('true','成功!');
   return to_json(res);
end;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100